(['/Users/<USER>/Code/订单超期/tools.py'],
 ['/Users/<USER>/Code/订单超期'],
 [],
 [('/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.13.1 (main, Dec  3 2024, 17:59:52) [Clang 16.0.0 (clang-1600.0.26.4)]',
 [('pyi_rth_inspect',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('tools', '/Users/<USER>/Code/订单超期/tools.py', 'PYSOURCE')],
 [('subprocess',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/subprocess.py',
   'PYMODULE'),
  ('selectors',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py',
   'PYMODULE'),
  ('contextlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py',
   'PYMODULE'),
  ('threading',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_threading_local.py',
   'PYMODULE'),
  ('signal',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/signal.py',
   'PYMODULE'),
  ('_strptime',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_strptime.py',
   'PYMODULE'),
  ('calendar',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/calendar.py',
   'PYMODULE'),
  ('argparse',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/textwrap.py',
   'PYMODULE'),
  ('shutil',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shutil.py',
   'PYMODULE'),
  ('zipfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/glob.py',
   'PYMODULE'),
  ('pathlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_local.py',
   'PYMODULE'),
  ('urllib.parse',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/parse.py',
   'PYMODULE'),
  ('urllib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/__init__.py',
   'PYMODULE'),
  ('ipaddress',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ipaddress.py',
   'PYMODULE'),
  ('glob',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/glob.py',
   'PYMODULE'),
  ('pathlib._abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_abc.py',
   'PYMODULE'),
  ('py_compile',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('csv',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/csv.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/quoprimime.py',
   'PYMODULE'),
  ('string',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/string.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/generator.py',
   'PYMODULE'),
  ('random',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/random.py',
   'PYMODULE'),
  ('statistics',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compat_pickle.py',
   'PYMODULE'),
  ('bisect',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bisect.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/base64.py',
   'PYMODULE'),
  ('getopt',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getopt.py',
   'PYMODULE'),
  ('email.charset',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.header',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/header.py',
   'PYMODULE'),
  ('email.errors',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/errors.py',
   'PYMODULE'),
  ('email.utils',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/utils.py',
   'PYMODULE'),
  ('socket',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_parseaddr.py',
   'PYMODULE'),
  ('quopri',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/quopri.py',
   'PYMODULE'),
  ('typing',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/typing.py',
   'PYMODULE'),
  ('importlib.abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tempfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tempfile.py',
   'PYMODULE'),
  ('importlib._abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('inspect',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/inspect.py',
   'PYMODULE'),
  ('token',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/token.py',
   'PYMODULE'),
  ('dis',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dis.py',
   'PYMODULE'),
  ('opcode',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/opcode.py',
   'PYMODULE'),
  ('_opcode_metadata',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_opcode_metadata.py',
   'PYMODULE'),
  ('ast',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ast.py',
   'PYMODULE'),
  ('email',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/parser.py',
   'PYMODULE'),
  ('email.feedparser',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/feedparser.py',
   'PYMODULE'),
  ('json',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/scanner.py',
   'PYMODULE'),
  ('__future__',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/__future__.py',
   'PYMODULE'),
  ('importlib.readers',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('tokenize',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tokenize.py',
   'PYMODULE'),
  ('struct',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/struct.py',
   'PYMODULE'),
  ('importlib.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/util.py',
   'PYMODULE'),
  ('tarfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gzip.py',
   'PYMODULE'),
  ('_compression',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compression.py',
   'PYMODULE'),
  ('lzma',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lzma.py',
   'PYMODULE'),
  ('bz2',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bz2.py',
   'PYMODULE'),
  ('fnmatch',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fnmatch.py',
   'PYMODULE'),
  ('copy',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/copy.py',
   'PYMODULE'),
  ('gettext',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gettext.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/request.py',
   'PYMODULE'),
  ('getpass',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/netrc.py',
   'PYMODULE'),
  ('mimetypes',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/__init__.py',
   'PYMODULE'),
  ('ssl',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py',
   'PYMODULE'),
  ('urllib.response',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/error.py',
   'PYMODULE'),
  ('xml.sax',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('http.client',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py',
   'PYMODULE'),
  ('hmac',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hmac.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/heap.py',
   'PYMODULE'),
  ('ctypes',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('queue',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py',
   'PYMODULE'),
  ('runpy',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/runpy.py',
   'PYMODULE'),
  ('pkgutil',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/msvc.py',
   'PYMODULE'),
  ('unittest.mock',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/mock.py',
   'PYMODULE'),
  ('unittest',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/_log.py',
   'PYMODULE'),
  ('difflib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/difflib.py',
   'PYMODULE'),
  ('unittest.result',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/util.py',
   'PYMODULE'),
  ('asyncio',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/compat/numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sysconfig/__init__.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_aix_support',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_aix_support.py',
   'PYMODULE'),
  ('_osx_support',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_osx_support.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('platform',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/platform.py',
   'PYMODULE'),
  ('_ios_support',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_ios_support.py',
   'PYMODULE'),
  ('plistlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/plistlib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('site',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site.py',
   'PYMODULE'),
  ('sitecustomize',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sitecustomize.py',
   'PYMODULE'),
  ('_pyrepl.main',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/main.py',
   'PYMODULE'),
  ('_pyrepl',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/__init__.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/curses.py',
   'PYMODULE'),
  ('curses',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/curses/has_key.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/keymap.py',
   'PYMODULE'),
  ('_pyrepl.types',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/types.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/commands.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/pager.py',
   'PYMODULE'),
  ('tty',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tty.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/reader.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/utils.py',
   'PYMODULE'),
  ('_colorize',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_colorize.py',
   'PYMODULE'),
  ('_pyrepl.console',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/console.py',
   'PYMODULE'),
  ('code',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/code.py',
   'PYMODULE'),
  ('codeop',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/codeop.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/trace.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/windows_console.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/wintypes.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/readline.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/completing_reader.py',
   'PYMODULE'),
  ('rlcompleter',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/webbrowser.py',
   'PYMODULE'),
  ('shlex',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shlex.py',
   'PYMODULE'),
  ('http.server',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py',
   'PYMODULE'),
  ('html',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc_data/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/configparser.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/zipp/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/zipp/glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('backports',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('tomllib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_types.py',
   'PYMODULE'),
  ('tomllib._re',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._shutil',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('stringprep',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tracemalloc.py',
   'PYMODULE'),
  ('datetime',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydatetime.py',
   'PYMODULE'),
  ('pandas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_typing.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/tseries/holiday.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil._version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('dateutil._common',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('six',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/six.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/__init__.py',
   'PYMODULE'),
  ('pandas.core',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arraylike.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/ops/common.py',
   'PYMODULE'),
  ('pandas.core.ops',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/ops/__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/ops/mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/ops/invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/ops/docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/ops/array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/ops/dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/check.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/compat/_optional.py',
   'PYMODULE'),
  ('pandas.util.version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/util/version/__init__.py',
   'PYMODULE'),
  ('pandas.util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/util/__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/util/hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/util/__init__.py',
   'PYMODULE'),
  ('pandas.core.computation',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/align.py',
   'PYMODULE'),
  ('pandas.util._validators',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/util/_validators.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/cast.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/groupby/ops.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/sorting.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/groupby/grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/groupby/categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/categorical.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/astype.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/strings/object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/strings/__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/strings/base.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/accessor.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/groupby/__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/groupby/groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/_numba/kernels/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/_numba/kernels/var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/_numba/kernels/shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/_numba/kernels/sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/_numba/kernels/min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/_numba/kernels/mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/window/__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/window/expanding.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/window/doc.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/shared_docs.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexers/objects.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/window/ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/window/online.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/window/numba_.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/window/common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/util/numba_.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/internals/blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/array_algos/transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/array_algos/replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/array_algos/quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/array_algos/putmask.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/groupby/indexing.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/masked.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/_utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/array_algos/masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/array_algos/masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/tools/numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/tools/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/arrow/_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/arrow/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/arrow/accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.apply',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/apply.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/_numba/extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/_numba/executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/_numba/__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/groupby/numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/groupby/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/arrow/extension_types.py',
   'PYMODULE'),
  ('pickletools',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pickletools.py',
   'PYMODULE'),
  ('doctest',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/doctest.py',
   'PYMODULE'),
  ('pdb',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pdb.py',
   'PYMODULE'),
  ('bdb',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bdb.py',
   'PYMODULE'),
  ('cmd',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/cmd.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/interval.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/tseries/frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexers/utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/array_algos/datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/array_algos/__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/compat/numpy/function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/compat/numpy/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/arrow/array.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/tools/times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/tools/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/tools/datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/integer.py',
   'PYMODULE'),
  ('pandas.io._util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/inference.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/util/_exceptions.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/ops/missing.py',
   'PYMODULE'),
  ('pandas.core.construction',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/construction.py',
   'PYMODULE'),
  ('numpy.ma',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/numerictypes.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_dtype.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/multiarray.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.umath',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/umath.py',
   'PYMODULE'),
  ('numpy._core._methods',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/introspect.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_machar.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/linalg/_linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/shape_base.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_npyio_impl.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/function_base.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/numeric.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/arrayprint.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/printoptions.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_asarray.py',
   'PYMODULE'),
  ('numpy._globals',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy._core',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.records',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/records.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/memmap.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/einsumfunc.py',
   'PYMODULE'),
  ('numpy.version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/version.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/algorithms.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/internals/construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/internals/managers.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/api/extensions/__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/internals/ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/internals/base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/internals/array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/array_algos/take.py',
   'PYMODULE'),
  ('pandas.core.sample',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/missing.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/roperator.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/util/_decorators.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/html.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/printing.py',
   'PYMODULE'),
  ('pandas.io.formats',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/_openpyxl.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/cell/cell.py',
   'PYMODULE'),
  ('openpyxl.cell',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/cell/__init__.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/cell/read_only.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/datetime.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/cell/rich_text.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/xml/functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/xml/__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/xml/constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/et_xmlfile/xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/et_xmlfile/incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/et_xmlfile/__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/etree/__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/descriptors/__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/descriptors/sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/descriptors/namespace.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/descriptors/base.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/cell/text.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/colors.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/descriptors/nested.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/hyperlink.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/descriptors/excel.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/protection.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/borders.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/fills.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/numbers.py',
   'PYMODULE'),
  ('openpyxl.utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/formula/__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/formula/tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/exceptions.py',
   'PYMODULE'),
  ('openpyxl.compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/compat/__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/compat/strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/compat/numbers.py',
   'PYMODULE'),
  ('openpyxl.styles',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/views.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/escape.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/packaging/relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/packaging/__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/descriptors/container.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/packaging/custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/packaging/core.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/defined_name.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chartsheet/__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chartsheet/chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chartsheet/publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chartsheet/custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chartsheet/views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chartsheet/protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chartsheet/properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chartsheet/relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/page.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/text.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/colors.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/shapes.py',
   'PYMODULE'),
  ('openpyxl.chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/reference.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/text.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/_3d.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/legend.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/area_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/line.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/xdr.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/drawing/image.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/units.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/table.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/differential.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/writer/excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/writer/__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/packaging/extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/writer/theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/packaging/workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/web.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/external_reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/cell/_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/utils/bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/styles/stylesheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/comments/comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/comments/__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/comments/shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/comments/comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/comments/author.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/packaging/manifest.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/pagebreak.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/formula/translate.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/formatting/formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/formatting/__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/formatting/rule.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/worksheet/print_settings.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/descriptors/serialisable.py',
   'PYMODULE'),
  ('openpyxl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/reader/excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/reader/__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/reader/drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/chart/reader.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/reader/workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/pivot/record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/pivot/__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/pivot/fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/pivot/cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/external_link/external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/workbook/external_link/__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/reader/strings.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/openpyxl/pivot/table.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/excel/_calamine.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/parsers/readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/parsers/python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/parsers/c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/parsers/base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/parsers/arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/parsers/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/api/types/__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/api.py',
   'PYMODULE'),
  ('uuid',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/uuid.py',
   'PYMODULE'),
  ('pandas.io.common',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/common.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/concat.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/range.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/datetimes.py',
   'PYMODULE'),
  ('pytz',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/api.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/period.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/category.py',
   'PYMODULE'),
  ('pandas.core.common',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/common.py',
   'PYMODULE'),
  ('pandas.core.base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/sparse/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/sparse/array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/sparse/accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/sparse/scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/common.py',
   'PYMODULE'),
  ('pandas._config.config',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_config/config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/window/rolling.py',
   'PYMODULE'),
  ('pandas.core.series',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/strings/accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/methods/selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/methods/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/accessors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/compat/_constants.py',
   'PYMODULE'),
  ('pandas.core.resample',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/internals/__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/internals/concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/internals/api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/indexes/base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/groupby/generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/clipboard/__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/compat/pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/sql.py',
   'PYMODULE'),
  ('sqlite3',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sqlite3/__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sqlite3/dump.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sqlite3/__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sqlite3/dbapi2.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/pytables.py',
   'PYMODULE'),
  ('pandas.io.json',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/json/__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/json/_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/json/_json.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/methods/describe.py',
   'PYMODULE'),
  ('pandas.core.flags',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/methods/to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/interchange/dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/interchange/utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/interchange/column.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/interchange/buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/interchange/dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/arrays/base.py',
   'PYMODULE'),
  ('numpy.typing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_exceptions.py',
   'PYMODULE'),
  ('numpy.strings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/strings/__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/strings.py',
   'PYMODULE'),
  ('numpy.core',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/core/_utils.py',
   'PYMODULE'),
  ('numpy.char',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/char/__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/rec/__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fileinput.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/_backends/__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/_backends/_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/_backends/_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/_backends/_meson.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/f2py/__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/ctypeslib/__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/ctypeslib/_ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/fft/_helper.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_array_api_info.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_expired_attrs_2_0.py',
   'PYMODULE'),
  ('pandas._libs.window',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/window/__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/sas/sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/sas/sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/sas/sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/sas/sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/sas/__init__.py',
   'PYMODULE'),
  ('pandas._libs',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/__init__.py',
   'PYMODULE'),
  ('pandas._version',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_version_meson.py',
   'PYMODULE'),
  ('pandas.util._tester',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/util/_tester.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/json/_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/spss.py',
   'PYMODULE'),
  ('pandas.io.html',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/util/_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/testing.py',
   'PYMODULE'),
  ('pandas._testing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_testing/__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_testing/contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_testing/compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_testing/asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_testing/_warnings.py',
   'PYMODULE'),
  ('pandas._testing._io',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_testing/_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_config/localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/plotting/__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/plotting/_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/plotting/_core.py',
   'PYMODULE'),
  ('pandas.io',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/errors/__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.api',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/api/__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/api/typing/__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/api/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/interchange/from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/api/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/reshape/encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/computation/api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/tseries/offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/tseries/__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/tseries/api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/dtypes/dtypes.py',
   'PYMODULE'),
  ('pandas.core.api',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/core/config_init.py',
   'PYMODULE'),
  ('pandas._config',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_config/__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_config/display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_config/dates.py',
   'PYMODULE'),
  ('pandas.compat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/compat/__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/compat/pyarrow.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/compat/compressors.py',
   'PYMODULE')],
 [('Python.framework/Versions/3.13/Python',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/Python',
   'BINARY'),
  ('lib-dynload/grp.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/grp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/math.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/select.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/fcntl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/unicodedata.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_statistics.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_contextvars.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_decimal.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_pickle.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_hashlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_blake2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_md5.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha1.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_random.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bisect.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/array.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_socket.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_opcode.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/binascii.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/resource.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_lzma.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bz2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_posixshmem.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_multiprocessing.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/pyexpat.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_scproxy.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/termios.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_ssl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/mmap.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_ctypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_queue.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/syslog.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_asyncio.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_curses.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/readline.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_multibytecodec.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_jp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_kr.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_cn.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_tw.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_hk.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_heapq.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/cmath.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/writers.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/writers.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_elementtree.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_uuid.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sqlite3.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/mtrand.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/bit_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/_sfc64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/_philox.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/_pcg64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/_mt19937.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/_common.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/random/_bounded_integers.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/indexers.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/window/indexers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/aggregations.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/window/aggregations.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/period.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/period.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/base.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslibs/base.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslib.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/tslib.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/testing.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/testing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sparse.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/sparse.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sas.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/sas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/reshape.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/reshape.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/properties.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/properties.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/parsers.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/parsers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_parser.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/pandas_parser.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/ops.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/missing.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/missing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/lib.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/lib.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/json.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/json.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/join.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/join.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/interval.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/interval.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/internals.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/internals.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/indexing.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/indexing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/index.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/index.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashtable.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/hashtable.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashing.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/hashing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/groupby.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/groupby.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/byteswap.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/byteswap.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/arrays.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/arrays.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/algos.cpython-313-darwin.so',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/_libs/algos.cpython-313-darwin.so',
   'EXTENSION'),
  ('libmpdec.4.dylib',
   '/opt/homebrew/opt/mpdecimal/lib/libmpdec.4.dylib',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/opt/homebrew/opt/openssl@3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('liblzma.5.dylib', '/opt/homebrew/opt/xz/lib/liblzma.5.dylib', 'BINARY'),
  ('libssl.3.dylib',
   '/opt/homebrew/opt/openssl@3/lib/libssl.3.dylib',
   'BINARY'),
  ('libsqlite3.0.dylib',
   '/opt/homebrew/opt/sqlite/lib/libsqlite3.0.dylib',
   'BINARY')],
 [],
 [],
 [('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas/io/formats/templates/html.tpl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/templates/html.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex.tpl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/templates/latex.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_style.tpl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/templates/html_style.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_table.tpl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/templates/html_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_table.tpl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/templates/latex_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_longtable.tpl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/templates/latex_longtable.tpl',
   'DATA'),
  ('pandas/io/formats/templates/string.tpl',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pandas/io/formats/templates/string.tpl',
   'DATA'),
  ('pytz/zoneinfo/UCT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/UCT',
   'DATA'),
  ('pytz/zoneinfo/America/Toronto',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Toronto',
   'DATA'),
  ('pytz/zoneinfo/America/Iqaluit',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Iqaluit',
   'DATA'),
  ('pytz/zoneinfo/Canada/Central',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Central',
   'DATA'),
  ('pytz/zoneinfo/America/Louisville',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Louisville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tashkent',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tashkent',
   'DATA'),
  ('pytz/zoneinfo/Asia/Almaty',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Almaty',
   'DATA'),
  ('pytz/zoneinfo/Europe/Saratov',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Saratov',
   'DATA'),
  ('pytz/zoneinfo/GB',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/GB',
   'DATA'),
  ('pytz/zoneinfo/Canada/Eastern',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zaporozhye',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zaporozhye',
   'DATA'),
  ('pytz/zoneinfo/Africa/Douala',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Douala',
   'DATA'),
  ('pytz/zoneinfo/Africa/Luanda',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Luanda',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashgabat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashgabat',
   'DATA'),
  ('pytz/zoneinfo/America/Goose_Bay',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Goose_Bay',
   'DATA'),
  ('pytz/zoneinfo/Indian/Comoro',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Comoro',
   'DATA'),
  ('pytz/zoneinfo/America/Jamaica',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Europe/Moscow',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Moscow',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tbilisi',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tbilisi',
   'DATA'),
  ('pytz/zoneinfo/Asia/Irkutsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Irkutsk',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaNorte',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaNorte',
   'DATA'),
  ('pytz/zoneinfo/Poland',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Poland',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Knox',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Knox',
   'DATA'),
  ('pytz/zoneinfo/America/Havana',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Havana',
   'DATA'),
  ('pytz/zoneinfo/US/Mountain',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT',
   'DATA'),
  ('pytz/zoneinfo/Australia/Hobart',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Hobart',
   'DATA'),
  ('pytz/zoneinfo/Europe/Prague',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Prague',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vientiane',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vientiane',
   'DATA'),
  ('pytz/zoneinfo/Africa/Algiers',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Algiers',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kirov',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kirov',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tomsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tomsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kyiv',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kyiv',
   'DATA'),
  ('pytz/zoneinfo/America/Ojinaga',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Ojinaga',
   'DATA'),
  ('pytz/zoneinfo/Africa/Niamey',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Niamey',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Syowa',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Syowa',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+9',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+9',
   'DATA'),
  ('pytz/zoneinfo/Indian/Christmas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Christmas',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tirane',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tirane',
   'DATA'),
  ('pytz/zoneinfo/America/Manaus',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Manaus',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zurich',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zurich',
   'DATA'),
  ('pytz/zoneinfo/Asia/Katmandu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Katmandu',
   'DATA'),
  ('pytz/zoneinfo/Australia/Broken_Hill',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Broken_Hill',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Buenos_Aires',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/NZ',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/NZ',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macao',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macao',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dar_es_Salaam',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dar_es_Salaam',
   'DATA'),
  ('pytz/zoneinfo/PST8PDT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/PST8PDT',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-4',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-4',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dushanbe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dushanbe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kathmandu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kathmandu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kabul',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kabul',
   'DATA'),
  ('pytz/zoneinfo/America/Lima',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Lima',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chungking',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chungking',
   'DATA'),
  ('pytz/zoneinfo/Canada/Newfoundland',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Newfoundland',
   'DATA'),
  ('pytz/zoneinfo/Australia/Queensland',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Queensland',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bamako',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bamako',
   'DATA'),
  ('pytz/zoneinfo/HST',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/HST',
   'DATA'),
  ('pytz/zoneinfo/America/Inuvik',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Inuvik',
   'DATA'),
  ('pytz/zoneinfo/Australia/South',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/South',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kaliningrad',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kaliningrad',
   'DATA'),
  ('pytz/zoneinfo/Chile/Continental',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Chile/Continental',
   'DATA'),
  ('pytz/zoneinfo/Australia/Darwin',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Darwin',
   'DATA'),
  ('pytz/zoneinfo/America/St_Vincent',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Vincent',
   'DATA'),
  ('pytz/zoneinfo/America/St_Johns',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Johns',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baku',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baku',
   'DATA'),
  ('pytz/zoneinfo/Africa/Brazzaville',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Brazzaville',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Rarotonga',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Rarotonga',
   'DATA'),
  ('pytz/zoneinfo/America/Tortola',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Tortola',
   'DATA'),
  ('pytz/zoneinfo/Chile/EasterIsland',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Chile/EasterIsland',
   'DATA'),
  ('pytz/zoneinfo/NZ-CHAT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/NZ-CHAT',
   'DATA'),
  ('pytz/zoneinfo/Africa/Porto-Novo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Porto-Novo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lagos',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lagos',
   'DATA'),
  ('pytz/zoneinfo/WET',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/WET',
   'DATA'),
  ('pytz/zoneinfo/Universal',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Universal',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Center',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Center',
   'DATA'),
  ('pytz/zoneinfo/America/Thule',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Thule',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Easter',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Easter',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aden',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aden',
   'DATA'),
  ('pytz/zoneinfo/America/Chihuahua',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Chihuahua',
   'DATA'),
  ('pytz/zoneinfo/Europe/Astrakhan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Astrakhan',
   'DATA'),
  ('pytz/zoneinfo/America/Montreal',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Montreal',
   'DATA'),
  ('pytz/zoneinfo/Asia/Urumqi',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Urumqi',
   'DATA'),
  ('pytz/zoneinfo/zone1970.tab',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/zone1970.tab',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tel_Aviv',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tel_Aviv',
   'DATA'),
  ('pytz/zoneinfo/America/Asuncion',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Asuncion',
   'DATA'),
  ('pytz/zoneinfo/Europe/Jersey',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Jersey',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-14',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-14',
   'DATA'),
  ('pytz/zoneinfo/Asia/Beirut',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Beirut',
   'DATA'),
  ('pytz/zoneinfo/America/Sitka',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Sitka',
   'DATA'),
  ('pytz/zoneinfo/Brazil/DeNoronha',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/DeNoronha',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/DumontDUrville',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/DumontDUrville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tokyo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tokyo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Johannesburg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Johannesburg',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Bermuda',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Bermuda',
   'DATA'),
  ('pytz/zoneinfo/Asia/Brunei',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Brunei',
   'DATA'),
  ('pytz/zoneinfo/America/Marigot',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Marigot',
   'DATA'),
  ('pytz/zoneinfo/Asia/Gaza',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Gaza',
   'DATA'),
  ('pytz/zoneinfo/America/Nassau',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Nassau',
   'DATA'),
  ('pytz/zoneinfo/Iran',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Iran',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Macquarie',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Macquarie',
   'DATA'),
  ('pytz/zoneinfo/Indian/Cocos',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Cocos',
   'DATA'),
  ('pytz/zoneinfo/America/St_Kitts',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Kitts',
   'DATA'),
  ('pytz/zoneinfo/MST7MDT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/MST7MDT',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Stanley',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Stanley',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lubumbashi',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lubumbashi',
   'DATA'),
  ('pytz/zoneinfo/Africa/Cairo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Cairo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mogadishu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mogadishu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Monaco',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Monaco',
   'DATA'),
  ('pytz/zoneinfo/Europe/Nicosia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bahrain',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bahrain',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ouagadougou',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ouagadougou',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Monticello',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Monticello',
   'DATA'),
  ('pytz/zoneinfo/America/Danmarkshavn',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Danmarkshavn',
   'DATA'),
  ('pytz/zoneinfo/Europe/Isle_of_Man',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Isle_of_Man',
   'DATA'),
  ('pytz/zoneinfo/Asia/Colombo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Colombo',
   'DATA'),
  ('pytz/zoneinfo/Australia/Adelaide',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Adelaide',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-11',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-11',
   'DATA'),
  ('pytz/zoneinfo/ROK',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/ROK',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Cordoba',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vienna',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vienna',
   'DATA'),
  ('pytz/zoneinfo/America/Denver',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Denver',
   'DATA'),
  ('pytz/zoneinfo/America/Scoresbysund',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Scoresbysund',
   'DATA'),
  ('pytz/zoneinfo/America/Boa_Vista',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Boa_Vista',
   'DATA'),
  ('pytz/zoneinfo/Australia/ACT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/ACT',
   'DATA'),
  ('pytz/zoneinfo/Australia/Canberra',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Canberra',
   'DATA'),
  ('pytz/zoneinfo/Canada/Pacific',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Pacific',
   'DATA'),
  ('pytz/zoneinfo/America/Guatemala',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Guatemala',
   'DATA'),
  ('pytz/zoneinfo/America/Rio_Branco',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Rio_Branco',
   'DATA'),
  ('pytz/zoneinfo/Africa/Juba',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Juba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qostanay',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qostanay',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dhaka',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dhaka',
   'DATA'),
  ('pytz/zoneinfo/America/Jujuy',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/Australia/Tasmania',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Tasmania',
   'DATA'),
  ('pytz/zoneinfo/Europe/London',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/London',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulaanbaatar',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulaanbaatar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Damascus',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Damascus',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pontianak',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pontianak',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vaduz',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vaduz',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Cape_Verde',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Cape_Verde',
   'DATA'),
  ('pytz/zoneinfo/America/Ensenada',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Ensenada',
   'DATA'),
  ('pytz/zoneinfo/America/Belize',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Belize',
   'DATA'),
  ('pytz/zoneinfo/Asia/Muscat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Muscat',
   'DATA'),
  ('pytz/zoneinfo/Australia/Yancowinna',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Yancowinna',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Acre',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Acre',
   'DATA'),
  ('pytz/zoneinfo/America/St_Thomas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Thomas',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vincennes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vincennes',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nouakchott',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nouakchott',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Marengo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Marengo',
   'DATA'),
  ('pytz/zoneinfo/America/Adak',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Adak',
   'DATA'),
  ('pytz/zoneinfo/America/Godthab',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Godthab',
   'DATA'),
  ('pytz/zoneinfo/Mexico/General',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/General',
   'DATA'),
  ('pytz/zoneinfo/America/Montevideo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Montevideo',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Galapagos',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Galapagos',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baghdad',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baghdad',
   'DATA'),
  ('pytz/zoneinfo/MST',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/MST',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yakutsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yakutsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Freetown',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Freetown',
   'DATA'),
  ('pytz/zoneinfo/Singapore',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Noumea',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Noumea',
   'DATA'),
  ('pytz/zoneinfo/America/Antigua',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Antigua',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson',
   'DATA'),
  ('pytz/zoneinfo/Asia/Makassar',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Makassar',
   'DATA'),
  ('pytz/zoneinfo/Africa/Banjul',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Banjul',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dakar',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dakar',
   'DATA'),
  ('pytz/zoneinfo/Australia/Victoria',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Victoria',
   'DATA'),
  ('pytz/zoneinfo/Etc/UCT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UCT',
   'DATA'),
  ('pytz/zoneinfo/Africa/Casablanca',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Casablanca',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ceuta',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ceuta',
   'DATA'),
  ('pytz/zoneinfo/Europe/Warsaw',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Warsaw',
   'DATA'),
  ('pytz/zoneinfo/America/Recife',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Recife',
   'DATA'),
  ('pytz/zoneinfo/Europe/Busingen',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Busingen',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lindeman',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lindeman',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kanton',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kanton',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Enderbury',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Enderbury',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novosibirsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novosibirsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qyzylorda',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qyzylorda',
   'DATA'),
  ('pytz/zoneinfo/Europe/Oslo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Oslo',
   'DATA'),
  ('pytz/zoneinfo/Europe/Copenhagen',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Copenhagen',
   'DATA'),
  ('pytz/zoneinfo/America/Eirunepe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Eirunepe',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ulyanovsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ulyanovsk',
   'DATA'),
  ('pytz/zoneinfo/America/Port_of_Spain',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Port_of_Spain',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Vostok',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Vostok',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chita',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chita',
   'DATA'),
  ('pytz/zoneinfo/Africa/Djibouti',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Djibouti',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belfast',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belfast',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bucharest',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bucharest',
   'DATA'),
  ('pytz/zoneinfo/Jamaica',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/EST',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/EST',
   'DATA'),
  ('pytz/zoneinfo/Asia/Choibalsan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Choibalsan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-7',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-7',
   'DATA'),
  ('pytz/zoneinfo/America/Sao_Paulo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Sao_Paulo',
   'DATA'),
  ('pytz/zoneinfo/Etc/Zulu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-5',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-5',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vatican',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vatican',
   'DATA'),
  ('pytz/zoneinfo/America/Cuiaba',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cuiaba',
   'DATA'),
  ('pytz/zoneinfo/Libya',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Libya',
   'DATA'),
  ('pytz/zoneinfo/America/Detroit',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Detroit',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Winamac',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Winamac',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulan_Bator',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulan_Bator',
   'DATA'),
  ('pytz/zoneinfo/America/Guadeloupe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Guadeloupe',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bangui',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bangui',
   'DATA'),
  ('pytz/zoneinfo/America/Catamarca',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Africa/Monrovia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Monrovia',
   'DATA'),
  ('pytz/zoneinfo/America/Ciudad_Juarez',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Ciudad_Juarez',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-10',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-10',
   'DATA'),
  ('pytz/zoneinfo/America/Winnipeg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Winnipeg',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faroe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faroe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Seoul',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Seoul',
   'DATA'),
  ('pytz/zoneinfo/Europe/Podgorica',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Podgorica',
   'DATA'),
  ('pytz/zoneinfo/Europe/Chisinau',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Chisinau',
   'DATA'),
  ('pytz/zoneinfo/America/Santiago',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Santiago',
   'DATA'),
  ('pytz/zoneinfo/America/Coral_Harbour',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Coral_Harbour',
   'DATA'),
  ('pytz/zoneinfo/ROC',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/ROC',
   'DATA'),
  ('pytz/zoneinfo/America/St_Barthelemy',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Barthelemy',
   'DATA'),
  ('pytz/zoneinfo/America/Indianapolis',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+11',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+11',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jayapura',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jayapura',
   'DATA'),
  ('pytz/zoneinfo/Navajo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Navajo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+6',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+6',
   'DATA'),
  ('pytz/zoneinfo/Asia/Nicosia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Taipei',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Taipei',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tahiti',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tahiti',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wallis',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wallis',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Midway',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Midway',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/New_Salem',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/New_Salem',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tiraspol',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tiraspol',
   'DATA'),
  ('pytz/zoneinfo/Africa/Conakry',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Conakry',
   'DATA'),
  ('pytz/zoneinfo/GMT+0',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/America/Cayenne',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayenne',
   'DATA'),
  ('pytz/zoneinfo/Europe/Luxembourg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Luxembourg',
   'DATA'),
  ('pytz/zoneinfo/Asia/Calcutta',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Calcutta',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaSur',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaSur',
   'DATA'),
  ('pytz/zoneinfo/Europe/Skopje',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Skopje',
   'DATA'),
  ('pytz/zoneinfo/America/Port-au-Prince',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Port-au-Prince',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guam',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guam',
   'DATA'),
  ('pytz/zoneinfo/Asia/Samarkand',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Samarkand',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Luis',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Luis',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vilnius',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vilnius',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jerusalem',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jerusalem',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/South_Pole',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/South_Pole',
   'DATA'),
  ('pytz/zoneinfo/America/Costa_Rica',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Costa_Rica',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Saipan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Saipan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-13',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-13',
   'DATA'),
  ('pytz/zoneinfo/Africa/Timbuktu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Timbuktu',
   'DATA'),
  ('pytz/zoneinfo/Africa/Libreville',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Libreville',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Truk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Truk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Minsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Minsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belgrade',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belgrade',
   'DATA'),
  ('pytz/zoneinfo/America/Halifax',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Halifax',
   'DATA'),
  ('pytz/zoneinfo/Asia/Famagusta',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Famagusta',
   'DATA'),
  ('pytz/zoneinfo/America/St_Lucia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Lucia',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Apia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Apia',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Azores',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Azores',
   'DATA'),
  ('pytz/zoneinfo/US/Eastern',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Australia/NSW',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/NSW',
   'DATA'),
  ('pytz/zoneinfo/Asia/Oral',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Oral',
   'DATA'),
  ('pytz/zoneinfo/America/Shiprock',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Shiprock',
   'DATA'),
  ('pytz/zoneinfo/America/Hermosillo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Hermosillo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+2',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+2',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fakaofo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fakaofo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+0',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Rothera',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Rothera',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bangkok',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bangkok',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mbabane',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mbabane',
   'DATA'),
  ('pytz/zoneinfo/Asia/Riyadh',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Riyadh',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmara',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmara',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guadalcanal',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guadalcanal',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dili',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dili',
   'DATA'),
  ('pytz/zoneinfo/Asia/Omsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Omsk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Nauru',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Nauru',
   'DATA'),
  ('pytz/zoneinfo/America/Juneau',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Juneau',
   'DATA'),
  ('pytz/zoneinfo/Iceland',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Iceland',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Ponape',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Ponape',
   'DATA'),
  ('pytz/zoneinfo/Europe/Mariehamn',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Mariehamn',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Velho',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Velho',
   'DATA'),
  ('pytz/zoneinfo/US/Hawaii',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Hawaii',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kamchatka',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kamchatka',
   'DATA'),
  ('pytz/zoneinfo/iso3166.tab',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/iso3166.tab',
   'DATA'),
  ('pytz/zoneinfo/Canada/Saskatchewan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Saskatchewan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Honolulu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Honolulu',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kinshasa',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kinshasa',
   'DATA'),
  ('pytz/zoneinfo/America/Barbados',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Barbados',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Majuro',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Majuro',
   'DATA'),
  ('pytz/zoneinfo/America/Miquelon',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Miquelon',
   'DATA'),
  ('pytz/zoneinfo/Australia/North',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/North',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Tucuman',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Tucuman',
   'DATA'),
  ('pytz/zoneinfo/Indian/Chagos',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Chagos',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macau',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macau',
   'DATA'),
  ('pytz/zoneinfo/EST5EDT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/EST5EDT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chongqing',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chongqing',
   'DATA'),
  ('pytz/zoneinfo/Cuba',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Cuba',
   'DATA'),
  ('pytz/zoneinfo/CET',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/CET',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kashgar',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kashgar',
   'DATA'),
  ('pytz/zoneinfo/America/Blanc-Sablon',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Blanc-Sablon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Helsinki',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Helsinki',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ljubljana',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ljubljana',
   'DATA'),
  ('pytz/zoneinfo/America/Managua',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Managua',
   'DATA'),
  ('pytz/zoneinfo/Indian/Reunion',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Reunion',
   'DATA'),
  ('pytz/zoneinfo/Africa/Khartoum',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Khartoum',
   'DATA'),
  ('pytz/zoneinfo/America/Santarem',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Santarem',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Auckland',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Auckland',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pyongyang',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pyongyang',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nairobi',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nairobi',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Marquesas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Marquesas',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kampala',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kampala',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hebron',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hebron',
   'DATA'),
  ('pytz/zoneinfo/America/Thunder_Bay',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Thunder_Bay',
   'DATA'),
  ('pytz/zoneinfo/America/Noronha',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Noronha',
   'DATA'),
  ('pytz/zoneinfo/America/Maceio',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Maceio',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-0',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/America/Yakutat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Yakutat',
   'DATA'),
  ('pytz/zoneinfo/America/Santa_Isabel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Santa_Isabel',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+8',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+8',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT0',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+5',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+5',
   'DATA'),
  ('pytz/zoneinfo/Europe/Rome',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Rome',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Salta',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Salta',
   'DATA'),
  ('pytz/zoneinfo/Asia/Barnaul',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Barnaul',
   'DATA'),
  ('pytz/zoneinfo/America/Nuuk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Nuuk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashkhabad',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashkhabad',
   'DATA'),
  ('pytz/zoneinfo/Zulu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Samara',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Samara',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-6',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-6',
   'DATA'),
  ('pytz/zoneinfo/Asia/Shanghai',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Shanghai',
   'DATA'),
  ('pytz/zoneinfo/Europe/Dublin',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Dublin',
   'DATA'),
  ('pytz/zoneinfo/America/Paramaribo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Paramaribo',
   'DATA'),
  ('pytz/zoneinfo/America/Guyana',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Guyana',
   'DATA'),
  ('pytz/zoneinfo/GMT0',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/GMT0',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Louisville',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Louisville',
   'DATA'),
  ('pytz/zoneinfo/America/Los_Angeles',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Los_Angeles',
   'DATA'),
  ('pytz/zoneinfo/Europe/Madrid',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Madrid',
   'DATA'),
  ('pytz/zoneinfo/Indian/Kerguelen',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Kerguelen',
   'DATA'),
  ('pytz/zoneinfo/PRC',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/PRC',
   'DATA'),
  ('pytz/zoneinfo/US/Central',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Central',
   'DATA'),
  ('pytz/zoneinfo/America/Boise',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Boise',
   'DATA'),
  ('pytz/zoneinfo/America/Phoenix',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Phoenix',
   'DATA'),
  ('pytz/zoneinfo/Europe/Berlin',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Berlin',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtau',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtau',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+3',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+3',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+4',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+4',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wake',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wake',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hovd',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hovd',
   'DATA'),
  ('pytz/zoneinfo/America/El_Salvador',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/El_Salvador',
   'DATA'),
  ('pytz/zoneinfo/Europe/Simferopol',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Simferopol',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-9',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-9',
   'DATA'),
  ('pytz/zoneinfo/Asia/Singapore',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Africa/Gaborone',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Gaborone',
   'DATA'),
  ('pytz/zoneinfo/America/Nipigon',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Nipigon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Gibraltar',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Gibraltar',
   'DATA'),
  ('pytz/zoneinfo/America/Chicago',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Chicago',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dubai',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dubai',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ho_Chi_Minh',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ho_Chi_Minh',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Samoa',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Volgograd',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Volgograd',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Palmer',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Palmer',
   'DATA'),
  ('pytz/zoneinfo/zonenow.tab',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/zonenow.tab',
   'DATA'),
  ('pytz/zoneinfo/America/Grand_Turk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Grand_Turk',
   'DATA'),
  ('pytz/zoneinfo/America/Bogota',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Bogota',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Indianapolis',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Asia/Atyrau',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Atyrau',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tripoli',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tripoli',
   'DATA'),
  ('pytz/zoneinfo/W-SU',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/W-SU',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Port_Moresby',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Port_Moresby',
   'DATA'),
  ('pytz/zoneinfo/America/Aruba',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Aruba',
   'DATA'),
  ('pytz/zoneinfo/zone.tab',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/zone.tab',
   'DATA'),
  ('pytz/zoneinfo/Canada/Mountain',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kigali',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kigali',
   'DATA'),
  ('pytz/zoneinfo/Brazil/Acre',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/Acre',
   'DATA'),
  ('pytz/zoneinfo/America/Metlakatla',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Metlakatla',
   'DATA'),
  ('pytz/zoneinfo/Africa/Accra',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Accra',
   'DATA'),
  ('pytz/zoneinfo/Australia/West',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/West',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Beulah',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Beulah',
   'DATA'),
  ('pytz/zoneinfo/Asia/Sakhalin',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Sakhalin',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tongatapu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tongatapu',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tunis',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tunis',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/La_Rioja',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/La_Rioja',
   'DATA'),
  ('pytz/zoneinfo/Japan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Japan',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maputo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maputo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimbu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimbu',
   'DATA'),
  ('pytz/zoneinfo/UTC',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/UTC',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tallinn',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tallinn',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bissau',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bissau',
   'DATA'),
  ('pytz/zoneinfo/America/Glace_Bay',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Glace_Bay',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yerevan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yerevan',
   'DATA'),
  ('pytz/zoneinfo/America/New_York',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/New_York',
   'DATA'),
  ('pytz/zoneinfo/Europe/Lisbon',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Lisbon',
   'DATA'),
  ('pytz/zoneinfo/America/Puerto_Rico',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Puerto_Rico',
   'DATA'),
  ('pytz/zoneinfo/America/Cayman',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayman',
   'DATA'),
  ('pytz/zoneinfo/US/Samoa',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/St_Helena',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/St_Helena',
   'DATA'),
  ('pytz/zoneinfo/leapseconds',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/leapseconds',
   'DATA'),
  ('pytz/zoneinfo/Europe/Amsterdam',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Amsterdam',
   'DATA'),
  ('pytz/zoneinfo/Africa/Windhoek',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Windhoek',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-8',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-8',
   'DATA'),
  ('pytz/zoneinfo/Factory',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Factory',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/South_Georgia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/South_Georgia',
   'DATA'),
  ('pytz/zoneinfo/Brazil/East',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/East',
   'DATA'),
  ('pytz/zoneinfo/America/Cordoba',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pago_Pago',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pago_Pago',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Funafuti',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Funafuti',
   'DATA'),
  ('pytz/zoneinfo/America/Knox_IN',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Knox_IN',
   'DATA'),
  ('pytz/zoneinfo/America/Tijuana',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Tijuana',
   'DATA'),
  ('pytz/zoneinfo/Europe/Guernsey',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Guernsey',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/McMurdo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/McMurdo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtobe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtobe',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Madeira',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Madeira',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mahe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mahe',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lord_Howe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lord_Howe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Magadan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Magadan',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Davis',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Davis',
   'DATA'),
  ('pytz/zoneinfo/Europe/Andorra',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Andorra',
   'DATA'),
  ('pytz/zoneinfo/America/Yellowknife',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Yellowknife',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Tell_City',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Tell_City',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Canary',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Canary',
   'DATA'),
  ('pytz/zoneinfo/Asia/Khandyga',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Khandyga',
   'DATA'),
  ('pytz/zoneinfo/Africa/El_Aaiun',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/El_Aaiun',
   'DATA'),
  ('pytz/zoneinfo/America/Mazatlan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Mazatlan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Bougainville',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Bougainville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Saigon',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Saigon',
   'DATA'),
  ('pytz/zoneinfo/America/Merida',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Merida',
   'DATA'),
  ('pytz/zoneinfo/Asia/Srednekolymsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Srednekolymsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novokuznetsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novokuznetsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmera',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmera',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Yap',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Yap',
   'DATA'),
  ('pytz/zoneinfo/America/Mendoza',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/America/Tegucigalpa',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Tegucigalpa',
   'DATA'),
  ('pytz/zoneinfo/EET',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/EET',
   'DATA'),
  ('pytz/zoneinfo/Etc/Universal',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Universal',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ust-Nera',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ust-Nera',
   'DATA'),
  ('pytz/zoneinfo/America/Kralendijk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Kralendijk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimphu',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimphu',
   'DATA'),
  ('pytz/zoneinfo/Australia/Eucla',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Eucla',
   'DATA'),
  ('pytz/zoneinfo/Etc/UTC',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UTC',
   'DATA'),
  ('pytz/zoneinfo/America/Nome',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Nome',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuala_Lumpur',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuala_Lumpur',
   'DATA'),
  ('pytz/zoneinfo/America/Edmonton',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Edmonton',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Wayne',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Wayne',
   'DATA'),
  ('pytz/zoneinfo/Europe/Brussels',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Brussels',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sofia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sofia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hong_Kong',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hong_Kong',
   'DATA'),
  ('pytz/zoneinfo/Africa/Blantyre',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Blantyre',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Mawson',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Mawson',
   'DATA'),
  ('pytz/zoneinfo/MET',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/MET',
   'DATA'),
  ('pytz/zoneinfo/Australia/LHI',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/LHI',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Catamarca',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Nelson',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Nelson',
   'DATA'),
  ('pytz/zoneinfo/Etc/Greenwich',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Reykjavik',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Reykjavik',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-2',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-2',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fiji',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fiji',
   'DATA'),
  ('pytz/zoneinfo/Asia/Manila',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Manila',
   'DATA'),
  ('pytz/zoneinfo/America/Caracas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Caracas',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-12',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-12',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Palau',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Palau',
   'DATA'),
  ('pytz/zoneinfo/Australia/Perth',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Perth',
   'DATA'),
  ('pytz/zoneinfo/Turkey',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Turkey',
   'DATA'),
  ('pytz/zoneinfo/America/Pangnirtung',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Pangnirtung',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Ushuaia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Ushuaia',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maseru',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maseru',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+7',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+7',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vevay',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vevay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Malta',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Malta',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson_Creek',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson_Creek',
   'DATA'),
  ('pytz/zoneinfo/Australia/Melbourne',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Melbourne',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Casey',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Casey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Istanbul',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/GMT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/GMT',
   'DATA'),
  ('pytz/zoneinfo/America/Rankin_Inlet',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Rankin_Inlet',
   'DATA'),
  ('pytz/zoneinfo/America/Punta_Arenas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Punta_Arenas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vladivostok',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vladivostok',
   'DATA'),
  ('pytz/zoneinfo/America/Resolute',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Resolute',
   'DATA'),
  ('pytz/zoneinfo/US/Indiana-Starke',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Indiana-Starke',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Troll',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Troll',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kosrae',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kosrae',
   'DATA'),
  ('pytz/zoneinfo/US/Alaska',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Alaska',
   'DATA'),
  ('pytz/zoneinfo/Israel',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Israel',
   'DATA'),
  ('pytz/zoneinfo/America/Belem',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Belem',
   'DATA'),
  ('pytz/zoneinfo/America/Coyhaique',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Coyhaique',
   'DATA'),
  ('pytz/zoneinfo/America/Monterrey',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Monterrey',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kolkata',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kolkata',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chuuk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chuuk',
   'DATA'),
  ('pytz/zoneinfo/US/Aleutian',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Aleutian',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chatham',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chatham',
   'DATA'),
  ('pytz/zoneinfo/Africa/Addis_Ababa',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Addis_Ababa',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kwajalein',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yangon',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yangon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kiev',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kiev',
   'DATA'),
  ('pytz/zoneinfo/Asia/Rangoon',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Rangoon',
   'DATA'),
  ('pytz/zoneinfo/Africa/Sao_Tome',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Sao_Tome',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+12',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+12',
   'DATA'),
  ('pytz/zoneinfo/CST6CDT',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/CST6CDT',
   'DATA'),
  ('pytz/zoneinfo/tzdata.zi',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/tzdata.zi',
   'DATA'),
  ('pytz/zoneinfo/America/Cancun',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cancun',
   'DATA'),
  ('pytz/zoneinfo/Africa/Harare',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Harare',
   'DATA'),
  ('pytz/zoneinfo/Asia/Harbin',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Harbin',
   'DATA'),
  ('pytz/zoneinfo/America/Atka',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Atka',
   'DATA'),
  ('pytz/zoneinfo/America/Montserrat',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Montserrat',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bishkek',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bishkek',
   'DATA'),
  ('pytz/zoneinfo/America/Araguaina',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Araguaina',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Niue',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Niue',
   'DATA'),
  ('pytz/zoneinfo/America/Campo_Grande',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Campo_Grande',
   'DATA'),
  ('pytz/zoneinfo/America/Vancouver',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Vancouver',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Norfolk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Norfolk',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Jan_Mayen',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Jan_Mayen',
   'DATA'),
  ('pytz/zoneinfo/Africa/Abidjan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Abidjan',
   'DATA'),
  ('pytz/zoneinfo/Arctic/Longyearbyen',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Arctic/Longyearbyen',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qatar',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qatar',
   'DATA'),
  ('pytz/zoneinfo/US/Arizona',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Arizona',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bujumbura',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bujumbura',
   'DATA'),
  ('pytz/zoneinfo/GB-Eire',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/GB-Eire',
   'DATA'),
  ('pytz/zoneinfo/US/Pacific',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Pacific',
   'DATA'),
  ('pytz/zoneinfo/Asia/Anadyr',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Anadyr',
   'DATA'),
  ('pytz/zoneinfo/Kwajalein',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/America/Anchorage',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Anchorage',
   'DATA'),
  ('pytz/zoneinfo/America/Martinique',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Martinique',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lome',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lome',
   'DATA'),
  ('pytz/zoneinfo/America/Dominica',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Dominica',
   'DATA'),
  ('pytz/zoneinfo/America/Guayaquil',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Guayaquil',
   'DATA'),
  ('pytz/zoneinfo/America/Anguilla',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Anguilla',
   'DATA'),
  ('pytz/zoneinfo/America/Buenos_Aires',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/Europe/San_Marino',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/San_Marino',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-3',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-3',
   'DATA'),
  ('pytz/zoneinfo/America/Panama',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Panama',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faeroe',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faeroe',
   'DATA'),
  ('pytz/zoneinfo/Eire',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Eire',
   'DATA'),
  ('pytz/zoneinfo/Australia/Sydney',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Sydney',
   'DATA'),
  ('pytz/zoneinfo/America/Swift_Current',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Swift_Current',
   'DATA'),
  ('pytz/zoneinfo/Europe/Stockholm',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Stockholm',
   'DATA'),
  ('pytz/zoneinfo/Indian/Antananarivo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Antananarivo',
   'DATA'),
  ('pytz/zoneinfo/Greenwich',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bratislava',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bratislava',
   'DATA'),
  ('pytz/zoneinfo/America/La_Paz',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/La_Paz',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuching',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuching',
   'DATA'),
  ('pytz/zoneinfo/Australia/Brisbane',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Brisbane',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zagreb',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zagreb',
   'DATA'),
  ('pytz/zoneinfo/Europe/Riga',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Riga',
   'DATA'),
  ('pytz/zoneinfo/US/Michigan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/Michigan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Johnston',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Johnston',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lusaka',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lusaka',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kiritimati',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kiritimati',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Efate',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Efate',
   'DATA'),
  ('pytz/zoneinfo/Africa/Malabo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Malabo',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mauritius',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mauritius',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Gambier',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Gambier',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Juan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Juan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Phnom_Penh',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Phnom_Penh',
   'DATA'),
  ('pytz/zoneinfo/Europe/Uzhgorod',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Uzhgorod',
   'DATA'),
  ('pytz/zoneinfo/Canada/Yukon',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Yukon',
   'DATA'),
  ('pytz/zoneinfo/America/Fortaleza',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Fortaleza',
   'DATA'),
  ('pytz/zoneinfo/America/Lower_Princes',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Lower_Princes',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-1',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-1',
   'DATA'),
  ('pytz/zoneinfo/Asia/Istanbul',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/GMT-0',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Mendoza',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/America/Grenada',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Grenada',
   'DATA'),
  ('pytz/zoneinfo/America/Mexico_City',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Mexico_City',
   'DATA'),
  ('pytz/zoneinfo/Portugal',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Portugal',
   'DATA'),
  ('pytz/zoneinfo/America/Santo_Domingo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Santo_Domingo',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia',
   'DATA'),
  ('pytz/zoneinfo/America/Virgin',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Virgin',
   'DATA'),
  ('pytz/zoneinfo/America/Menominee',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Menominee',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sarajevo',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sarajevo',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mayotte',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mayotte',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pohnpei',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pohnpei',
   'DATA'),
  ('pytz/zoneinfo/Asia/Amman',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Amman',
   'DATA'),
  ('pytz/zoneinfo/Egypt',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Egypt',
   'DATA'),
  ('pytz/zoneinfo/US/East-Indiana',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/US/East-Indiana',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Jujuy',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/America/Cambridge_Bay',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Cambridge_Bay',
   'DATA'),
  ('pytz/zoneinfo/Hongkong',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Hongkong',
   'DATA'),
  ('pytz/zoneinfo/America/Moncton',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Moncton',
   'DATA'),
  ('pytz/zoneinfo/Brazil/West',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/West',
   'DATA'),
  ('pytz/zoneinfo/Australia/Currie',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Currie',
   'DATA'),
  ('pytz/zoneinfo/Europe/Budapest',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Budapest',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+1',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+1',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuwait',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuwait',
   'DATA'),
  ('pytz/zoneinfo/America/Creston',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Creston',
   'DATA'),
  ('pytz/zoneinfo/America/Whitehorse',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Whitehorse',
   'DATA'),
  ('pytz/zoneinfo/Europe/Paris',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Paris',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ujung_Pandang',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ujung_Pandang',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+10',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+10',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yekaterinburg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yekaterinburg',
   'DATA'),
  ('pytz/zoneinfo/Indian/Maldives',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Maldives',
   'DATA'),
  ('pytz/zoneinfo/America/Curacao',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Curacao',
   'DATA'),
  ('pytz/zoneinfo/Europe/Athens',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Athens',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia_Banderas',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia_Banderas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Karachi',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Karachi',
   'DATA'),
  ('pytz/zoneinfo/America/Rosario',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Rosario',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ndjamena',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ndjamena',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tarawa',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tarawa',
   'DATA'),
  ('pytz/zoneinfo/America/Regina',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Regina',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/ComodRivadavia',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/ComodRivadavia',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Petersburg',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Petersburg',
   'DATA'),
  ('pytz/zoneinfo/Canada/Atlantic',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Atlantic',
   'DATA'),
  ('pytz/zoneinfo/America/Rainy_River',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Rainy_River',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tehran',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tehran',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jakarta',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jakarta',
   'DATA'),
  ('pytz/zoneinfo/Asia/Krasnoyarsk',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Krasnoyarsk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pitcairn',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pitcairn',
   'DATA'),
  ('pytz/zoneinfo/America/Matamoros',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Matamoros',
   'DATA'),
  ('pytz/zoneinfo/America/Atikokan',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/America/Atikokan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dacca',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dacca',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.13/Python', 'SYMLINK'),
  ('numpy-2.3.2.dist-info/METADATA',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy-2.3.2.dist-info/METADATA',
   'DATA'),
  ('numpy-2.3.2.dist-info/RECORD',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy-2.3.2.dist-info/RECORD',
   'DATA'),
  ('numpy-2.3.2.dist-info/INSTALLER',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy-2.3.2.dist-info/INSTALLER',
   'DATA'),
  ('numpy-2.3.2.dist-info/WHEEL',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy-2.3.2.dist-info/WHEEL',
   'DATA'),
  ('numpy-2.3.2.dist-info/entry_points.txt',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy-2.3.2.dist-info/entry_points.txt',
   'DATA'),
  ('numpy-2.3.2.dist-info/LICENSE.txt',
   '/Users/<USER>/Code/订单超期/myenv/lib/python3.13/site-packages/numpy-2.3.2.dist-info/LICENSE.txt',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Code/订单超期/build/tools/base_library.zip',
   'DATA'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.13/Resources/Info.plist',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.13', 'SYMLINK')],
 [('linecache',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/linecache.py',
   'PYMODULE'),
  ('copyreg',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/copyreg.py',
   'PYMODULE'),
  ('sre_compile',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sre_compile.py',
   'PYMODULE'),
  ('codecs',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/codecs.py',
   'PYMODULE'),
  ('stat',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/stat.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/idna.py',
   'PYMODULE'),
  ('encodings.hz',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/aliases.py',
   'PYMODULE'),
  ('encodings',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/__init__.py',
   'PYMODULE'),
  ('heapq',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/heapq.py',
   'PYMODULE'),
  ('traceback',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/traceback.py',
   'PYMODULE'),
  ('genericpath',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/genericpath.py',
   'PYMODULE'),
  ('sre_parse',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sre_parse.py',
   'PYMODULE'),
  ('enum',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/enum.py',
   'PYMODULE'),
  ('operator',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/operator.py',
   'PYMODULE'),
  ('_collections_abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_collections_abc.py',
   'PYMODULE'),
  ('collections',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/collections/__init__.py',
   'PYMODULE'),
  ('locale',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/locale.py',
   'PYMODULE'),
  ('abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/abc.py',
   'PYMODULE'),
  ('reprlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/reprlib.py',
   'PYMODULE'),
  ('re._parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_parser.py',
   'PYMODULE'),
  ('re._constants',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_constants.py',
   'PYMODULE'),
  ('re._compiler',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_casefix.py',
   'PYMODULE'),
  ('re',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/__init__.py',
   'PYMODULE'),
  ('sre_constants',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sre_constants.py',
   'PYMODULE'),
  ('keyword',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/keyword.py',
   'PYMODULE'),
  ('ntpath',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ntpath.py',
   'PYMODULE'),
  ('functools',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/functools.py',
   'PYMODULE'),
  ('os',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/os.py',
   'PYMODULE'),
  ('io',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/io.py',
   'PYMODULE'),
  ('types',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/types.py',
   'PYMODULE'),
  ('posixpath',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/posixpath.py',
   'PYMODULE'),
  ('warnings',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/warnings.py',
   'PYMODULE'),
  ('_weakrefset',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_weakrefset.py',
   'PYMODULE'),
  ('weakref',
   '/opt/homebrew/Cellar/python@3.13/3.13.1/Frameworks/Python.framework/Versions/3.13/lib/python3.13/weakref.py',
   'PYMODULE')])
