# pip install pandas openpyxl tkinter
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime
import json
import os

class OrderOverdueAnalyzer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("订单超期分析工具")
        self.root.geometry("800x700")
        
        # 配置文件路径
        self.config_file = "config.json"
        
        # 默认配置
        self.config = {
            "input_file": "",
            "sheet_name": "",
            "output_file": "超期提醒.xlsx",
            "nodes": [
                {"name": "签约客户审核通过", "days": 7, "prev_col": "订单生成时间"},
                {"name": "发货总部审核通过", "days": 14, "prev_col": "签约客户审核通过时间"},
                {"name": "施工总部审核通过", "days": 25, "prev_col": "发货总部审核通过时间"},
                {"name": "首次并网总部审核通过", "days": 45, "prev_col": "施工总部审核通过时间"}
            ]
        }
        
        # 加载配置
        self.load_config()
        
        # 创建UI
        self.create_widgets()
        
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 输入文件选择
        ttk.Label(file_frame, text="输入文件:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.input_file_var = tk.StringVar(value=self.config["input_file"])
        ttk.Entry(file_frame, textvariable=self.input_file_var, width=50).grid(row=0, column=1, padx=(5, 5), pady=2)
        ttk.Button(file_frame, text="浏览", command=self.select_input_file).grid(row=0, column=2, pady=2)
        
        # Sheet选择
        ttk.Label(file_frame, text="工作表:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.sheet_var = tk.StringVar(value=self.config["sheet_name"])
        self.sheet_combo = ttk.Combobox(file_frame, textvariable=self.sheet_var, width=47)
        self.sheet_combo.grid(row=1, column=1, padx=(5, 5), pady=2)
        ttk.Button(file_frame, text="刷新", command=self.refresh_sheets).grid(row=1, column=2, pady=2)
        
        # 输出文件选择
        ttk.Label(file_frame, text="输出文件:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.output_file_var = tk.StringVar(value=self.config["output_file"])
        ttk.Entry(file_frame, textvariable=self.output_file_var, width=50).grid(row=2, column=1, padx=(5, 5), pady=2)
        ttk.Button(file_frame, text="浏览", command=self.select_output_file).grid(row=2, column=2, pady=2)
        
        # 节点配置区域
        nodes_frame = ttk.LabelFrame(main_frame, text="节点配置", padding="10")
        nodes_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 节点列表
        columns = ("节点名称", "容忍天数", "前置节点")
        self.nodes_tree = ttk.Treeview(nodes_frame, columns=columns, show="headings", height=6)
        
        for col in columns:
            self.nodes_tree.heading(col, text=col)
            self.nodes_tree.column(col, width=200)
        
        self.nodes_tree.grid(row=0, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 节点操作按钮
        ttk.Button(nodes_frame, text="添加节点", command=self.add_node).grid(row=1, column=0, padx=(0, 5))
        ttk.Button(nodes_frame, text="编辑节点", command=self.edit_node).grid(row=1, column=1, padx=5)
        ttk.Button(nodes_frame, text="删除节点", command=self.delete_node).grid(row=1, column=2, padx=5)
        ttk.Button(nodes_frame, text="重置默认", command=self.reset_nodes).grid(row=1, column=3, padx=(5, 0))
        
        # 操作按钮区域
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=2, column=0, columnspan=2, pady=(0, 10))
        
        ttk.Button(action_frame, text="保存配置", command=self.save_config).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(action_frame, text="加载配置", command=self.load_config).grid(row=0, column=1, padx=10)
        ttk.Button(action_frame, text="开始分析", command=self.analyze_overdue).grid(row=0, column=2, padx=(10, 0))
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="分析结果", padding="10")
        result_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.result_text = tk.Text(result_frame, height=10, width=80)
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 初始化节点显示
        self.refresh_nodes_display()
        
    def select_input_file(self):
        filename = filedialog.askopenfilename(
            title="选择输入文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.input_file_var.set(filename)
            self.refresh_sheets()
            
    def select_output_file(self):
        filename = filedialog.asksaveasfilename(
            title="选择输出文件",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.output_file_var.set(filename)
            
    def refresh_sheets(self):
        input_file = self.input_file_var.get()
        if not input_file or not os.path.exists(input_file):
            self.sheet_combo['values'] = []
            return
            
        try:
            excel_file = pd.ExcelFile(input_file)
            sheets = excel_file.sheet_names
            self.sheet_combo['values'] = sheets
            if sheets and not self.sheet_var.get():
                self.sheet_var.set(sheets[0])
        except Exception as e:
            messagebox.showerror("错误", f"读取Excel文件失败: {str(e)}")
            self.sheet_combo['values'] = []
            
    def refresh_nodes_display(self):
        # 清空现有项目
        for item in self.nodes_tree.get_children():
            self.nodes_tree.delete(item)
            
        # 添加节点
        for node in self.config["nodes"]:
            self.nodes_tree.insert("", "end", values=(
                node["name"], 
                node["days"], 
                node["prev_col"]
            ))
            
    def add_node(self):
        self.edit_node_dialog()
        
    def edit_node(self):
        selected = self.nodes_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个节点")
            return
            
        item = selected[0]
        values = self.nodes_tree.item(item, "values")
        self.edit_node_dialog(values)
        
    def edit_node_dialog(self, values=None):
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑节点" if values else "添加节点")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 节点名称
        ttk.Label(dialog, text="节点名称:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        name_var = tk.StringVar(value=values[0] if values else "")
        ttk.Entry(dialog, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=5)
        
        # 容忍天数
        ttk.Label(dialog, text="容忍天数:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        days_var = tk.StringVar(value=values[1] if values else "")
        ttk.Entry(dialog, textvariable=days_var, width=30).grid(row=1, column=1, padx=10, pady=5)
        
        # 前置节点
        ttk.Label(dialog, text="前置节点:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        prev_var = tk.StringVar(value=values[2] if values else "")
        ttk.Entry(dialog, textvariable=prev_var, width=30).grid(row=2, column=1, padx=10, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        def save_node():
            try:
                name = name_var.get().strip()
                days = int(days_var.get().strip())
                prev_col = prev_var.get().strip()
                
                if not name or not prev_col:
                    messagebox.showerror("错误", "请填写所有字段")
                    return
                    
                node_data = {"name": name, "days": days, "prev_col": prev_col}
                
                if values:  # 编辑模式
                    # 找到对应的节点并更新
                    for i, node in enumerate(self.config["nodes"]):
                        if (node["name"] == values[0] and 
                            node["days"] == int(values[1]) and 
                            node["prev_col"] == values[2]):
                            self.config["nodes"][i] = node_data
                            break
                else:  # 添加模式
                    self.config["nodes"].append(node_data)
                    
                self.refresh_nodes_display()
                dialog.destroy()
                
            except ValueError:
                messagebox.showerror("错误", "容忍天数必须是数字")
                
        ttk.Button(button_frame, text="保存", command=save_node).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).grid(row=0, column=1, padx=5)
        
    def delete_node(self):
        selected = self.nodes_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个节点")
            return
            
        if messagebox.askyesno("确认", "确定要删除选中的节点吗？"):
            item = selected[0]
            values = self.nodes_tree.item(item, "values")
            
            # 从配置中删除
            self.config["nodes"] = [
                node for node in self.config["nodes"]
                if not (node["name"] == values[0] and 
                       node["days"] == int(values[1]) and 
                       node["prev_col"] == values[2])
            ]
            
            self.refresh_nodes_display()
            
    def reset_nodes(self):
        if messagebox.askyesno("确认", "确定要重置为默认节点配置吗？"):
            self.config["nodes"] = [
                {"name": "签约客户审核通过", "days": 7, "prev_col": "订单生成时间"},
                {"name": "发货总部审核通过", "days": 14, "prev_col": "签约客户审核通过时间"},
                {"name": "施工总部审核通过", "days": 25, "prev_col": "发货总部审核通过时间"},
                {"name": "首次并网总部审核通过", "days": 45, "prev_col": "施工总部审核通过时间"}
            ]
            self.refresh_nodes_display()

    def save_config(self):
        # 更新配置
        self.config["input_file"] = self.input_file_var.get()
        self.config["sheet_name"] = self.sheet_var.get()
        self.config["output_file"] = self.output_file_var.get()

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)

                # 更新UI
                self.input_file_var.set(self.config["input_file"])
                self.sheet_var.set(self.config["sheet_name"])
                self.output_file_var.set(self.config["output_file"])
                self.refresh_nodes_display()
                self.refresh_sheets()

                messagebox.showinfo("成功", "配置已加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def analyze_overdue(self):
        # 验证输入
        input_file = self.input_file_var.get()
        sheet_name = self.sheet_var.get()
        output_file = self.output_file_var.get()

        if not input_file or not os.path.exists(input_file):
            messagebox.showerror("错误", "请选择有效的输入文件")
            return

        if not sheet_name:
            messagebox.showerror("错误", "请选择工作表")
            return

        if not output_file:
            messagebox.showerror("错误", "请指定输出文件")
            return

        if not self.config["nodes"]:
            messagebox.showerror("错误", "请至少配置一个节点")
            return

        try:
            # 清空结果显示
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析...\n")
            self.root.update()

            # 读取数据
            df = pd.read_excel(input_file, sheet_name=sheet_name)
            self.result_text.insert(tk.END, f"读取数据完成，共 {len(df)} 行\n")
            self.root.update()

            # 只保留"正常"订单
            if '订单状态' in df.columns:
                df = df[df['订单状态'] == '正常'].copy()
                self.result_text.insert(tk.END, f"筛选正常订单后，共 {len(df)} 行\n")
                self.root.update()

            # 获取今天日期
            TODAY = pd.Timestamp(datetime.today().date())

            # 日期列统一转为 datetime
            date_cols = [
                '订单生成时间', '签约客户审核通过时间',
                '发货总部审核通过时间', '施工总部审核通过时间',
                '首次并网总部审核通过时间'
            ]

            # 添加配置中的前置节点列
            for node in self.config["nodes"]:
                if node["prev_col"] not in date_cols:
                    date_cols.append(node["prev_col"])

            # 转换日期列
            for col in date_cols:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')

            # 收集超期记录
            overdue_records = []

            for _, row in df.iterrows():
                for node in self.config["nodes"]:
                    node_name = node["name"]
                    limit = node["days"]
                    prev_col = node["prev_col"]

                    # 当前节点时间列名
                    current_col = f'{node_name}时间'

                    # 检查列是否存在
                    if current_col not in df.columns:
                        continue

                    # 当前节点为空才检查
                    if pd.isna(row[current_col]):
                        # 若前置节点也没有，则跳过
                        if prev_col not in df.columns or pd.isna(row[prev_col]):
                            continue

                        due = row[prev_col] + pd.Timedelta(days=limit)
                        if TODAY > due:
                            record = {
                                '超期节点': node_name,
                                '应完成日期': due.date(),
                                '已延迟天数': (TODAY - due).days
                            }

                            # 添加其他可用列
                            for col in ['城市经理', '代理商名称', '订单号', '客户名称', '项目名称']:
                                if col in df.columns:
                                    record[col] = row[col]

                            overdue_records.append(record)

            # 生成结果
            if overdue_records:
                result_df = pd.DataFrame(overdue_records)
                result_df.to_excel(output_file, index=False)

                self.result_text.insert(tk.END, f"\n分析完成！\n")
                self.result_text.insert(tk.END, f"共发现 {len(overdue_records)} 条超期记录\n")
                self.result_text.insert(tk.END, f"结果已保存到: {output_file}\n\n")

                # 显示统计信息
                self.result_text.insert(tk.END, "超期节点统计:\n")
                node_stats = result_df['超期节点'].value_counts()
                for node, count in node_stats.items():
                    self.result_text.insert(tk.END, f"  {node}: {count} 条\n")

                # 显示前几条记录
                self.result_text.insert(tk.END, f"\n前 {min(5, len(result_df))} 条记录:\n")
                for i, (_, row) in enumerate(result_df.head().iterrows()):
                    self.result_text.insert(tk.END, f"{i+1}. ")
                    if '订单号' in row:
                        self.result_text.insert(tk.END, f"订单号: {row['订单号']}, ")
                    self.result_text.insert(tk.END, f"超期节点: {row['超期节点']}, ")
                    self.result_text.insert(tk.END, f"延迟: {row['已延迟天数']} 天\n")

            else:
                self.result_text.insert(tk.END, "\n分析完成！\n")
                self.result_text.insert(tk.END, "未发现超期记录\n")

        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")
            self.result_text.insert(tk.END, f"\n错误: {str(e)}\n")

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = OrderOverdueAnalyzer()
    app.run()
