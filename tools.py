# pip install pandas openpyxl
import pandas as pd
from datetime import datetime

TODAY = pd.Timestamp(datetime.today().date())

# 读取
file_path = '订单2.xlsx'
sheet_name = '订单数据-SPV'
df = pd.read_excel(file_path, sheet_name=sheet_name)

# 只保留“正常”订单
df = df[df['订单状态'] == '正常'].copy()

# 日期列统一转为 datetime
date_cols = [
    '订单生成时间', '签约客户审核通过时间',
    '发货总部审核通过时间', '施工总部审核通过时间',
    '首次并网总部审核通过时间'
]
for col in date_cols:
    df[col] = pd.to_datetime(df[col], errors='coerce')

# 节点及容忍天数
nodes = [
    ('签约客户审核通过', 7,  '订单生成时间'),
    ('发货总部审核通过', 14, '签约客户审核通过时间'),
    ('施工总部审核通过', 25, '发货总部审核通过时间'),
    ('首次并网总部审核通过', 45, '施工总部审核通过时间')
]

# 收集超期记录
overdue_records = []

for _, row in df.iterrows():
    for node, limit, prev_col in nodes:
        # 当前节点为空才检查
        if pd.isna(row[f'{node}时间']):
            # 若前置节点也没有，则跳过
            if pd.isna(row[prev_col]):
                continue
            due = row[prev_col] + pd.Timedelta(days=limit)
            if TODAY > due:
                overdue_records.append({
                    '城市经理': row['城市经理'],
                    '代理商名称': row['代理商名称'],
                    '订单号': row['订单号'],
                    '超期节点': node,
                    '应完成日期': due.date(),
                    '已延迟天数': (TODAY - due).days
                })

# 生成结果
result = pd.DataFrame(overdue_records, columns=[
    '城市经理', '代理商名称', '订单号',
    '超期节点', '应完成日期', '已延迟天数'
])

result.to_excel('超期提醒.xlsx', index=False)
print('已生成超期提醒.xlsx，共', len(result), '条提醒')