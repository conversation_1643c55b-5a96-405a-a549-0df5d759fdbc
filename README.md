# 订单超期分析工具

这是一个带有图形用户界面的订单超期分析工具，用于分析Excel文件中的订单数据，识别超期的订单节点。

## 功能特性

### 1. 文件选择功能
- **输入文件选择**: 支持选择Excel文件（.xlsx, .xls格式）
- **工作表选择**: 自动读取Excel文件中的所有工作表，支持下拉选择
- **输出文件选择**: 支持自定义输出文件路径和名称

### 2. 节点配置功能
- **节点管理**: 支持添加、编辑、删除节点
- **容忍天数设置**: 为每个节点设置不同的容忍天数
- **前置节点配置**: 设置每个节点的前置节点列名
- **预设配置**: 支持不同工作表使用不同的预设节点配置
- **智能切换**: 选择不同工作表时自动加载对应的节点配置
- **重置功能**: 根据当前工作表重置为对应的默认配置

### 3. 配置保存功能
- **配置保存**: 将当前设置保存到config.json文件
- **配置加载**: 从配置文件加载之前保存的设置
- **工作表配置**: 为每个工作表单独保存节点配置
- **自动加载**: 程序启动时自动加载上次的配置

### 4. 分析结果展示
- **实时进度**: 显示分析过程中的实时进度信息
- **统计信息**: 显示各节点的超期统计
- **详细记录**: 显示前几条超期记录的详细信息

## 使用方法

### 1. 安装依赖
```bash
pip install pandas openpyxl tkinter
```

### 2. 运行程序
```bash
python tools_ui.py
```

### 3. 使用步骤
1. **选择输入文件**: 点击"浏览"按钮选择包含订单数据的Excel文件
2. **选择工作表**: 从下拉列表中选择要分析的工作表
3. **配置节点**:
   - 程序会根据选择的工作表自动加载对应的预设配置
   - 支持的预设工作表：`订单数据-SPV`、`订单数据-ERP`
   - 可以使用"加载预设"按钮手动选择其他预设配置
   - 如需自定义，可以添加、编辑或删除节点
   - 每个节点需要设置：节点名称、容忍天数、前置节点列名
4. **选择输出文件**: 指定分析结果的保存位置
5. **开始分析**: 点击"开始分析"按钮执行分析
6. **查看结果**: 在结果区域查看分析进度和统计信息

### 4. 配置管理
- **保存配置**: 点击"保存配置"将当前设置保存到文件
- **加载配置**: 点击"加载配置"从文件加载之前保存的设置

## 预设节点配置

程序为不同的工作表预设了对应的节点配置：

### 订单数据-SPV 预设配置
| 节点名称 | 容忍天数 | 前置节点 |
|---------|---------|---------|
| 签约客户审核通过 | 7天 | 订单生成时间 |
| 发货总部审核通过 | 14天 | 签约客户审核通过时间 |
| 施工总部审核通过 | 25天 | 发货总部审核通过时间 |
| 首次并网总部审核通过 | 45天 | 施工总部审核通过时间 |

### 订单数据-ERP 预设配置
| 节点名称 | 容忍天数 | 前置节点 |
|---------|---------|---------|
| 华夏审核通过时间 | 7天 | 订单生成时间 |
| 最后一批出库清单确认时间 | 14天 | 华夏审核通过时间 |
| 施工终审通过时间 | 25天 | 最后一批出库清单确认时间 |
| 首次并网技术审核通过时间 | 45天 | 施工终审通过时间 |

## 数据要求

### Excel文件格式要求
- 文件格式：.xlsx 或 .xls
- 必须包含"订单状态"列（程序会筛选"正常"状态的订单）
- 必须包含相应的时间列，列名格式为："{节点名称}时间"

### 必需的列
- `订单状态`: 用于筛选正常订单
- `订单生成时间`: 作为第一个节点的前置时间
- `{节点名称}时间`: 每个配置节点对应的时间列
- 其他信息列（可选）：`城市经理`、`代理商名称`、`订单号`、`客户名称`、`项目名称`

## 输出结果

分析完成后会生成Excel文件，包含以下列：
- **超期节点**: 超期的节点名称
- **应完成日期**: 该节点应该完成的日期
- **已延迟天数**: 已经延迟的天数
- **其他信息列**: 如城市经理、代理商名称、订单号等（如果原数据中存在）

## 注意事项

1. **日期格式**: 程序会自动尝试解析各种日期格式，但建议使用标准的日期格式
2. **列名匹配**: 节点时间列名必须严格按照"{节点名称}时间"的格式
3. **数据完整性**: 如果前置节点时间为空，该节点不会被检查
4. **配置文件**: config.json文件会保存在程序同目录下

## 故障排除

### 常见问题
1. **无法读取Excel文件**: 检查文件是否损坏，是否为支持的格式
2. **工作表列表为空**: 确认Excel文件包含有效的工作表
3. **分析结果为空**: 检查数据中是否存在相应的时间列，以及日期格式是否正确
4. **节点配置错误**: 确认节点名称和前置节点列名与Excel中的列名一致

### 错误信息
程序会在结果区域显示详细的错误信息，帮助定位问题。
