#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单超期分析工具演示脚本
展示如何使用不同工作表的预设配置
"""

import pandas as pd
from datetime import datetime
import json

def demo_sheet_configs():
    """演示不同工作表的配置"""
    
    # 预设配置
    sheet_presets = {
        "订单数据-SPV": [
            {"name": "签约客户审核通过", "days": 7, "prev_col": "订单生成时间"},
            {"name": "发货总部审核通过", "days": 14, "prev_col": "签约客户审核通过时间"},
            {"name": "施工总部审核通过", "days": 25, "prev_col": "发货总部审核通过时间"},
            {"name": "首次并网总部审核通过", "days": 45, "prev_col": "施工总部审核通过时间"}
        ],
        "订单数据-ERP": [
            {"name": "华夏审核通过时间", "days": 7, "prev_col": "订单生成时间"},
            {"name": "最后一批出库清单确认时间", "days": 14, "prev_col": "华夏审核通过时间"},
            {"name": "施工终审通过时间", "days": 25, "prev_col": "最后一批出库清单确认时间"},
            {"name": "首次并网技术审核通过时间", "days": 45, "prev_col": "施工终审通过时间"}
        ]
    }
    
    print("=== 订单超期分析工具 - 工作表配置演示 ===\n")
    
    for sheet_name, nodes in sheet_presets.items():
        print(f"📊 工作表: {sheet_name}")
        print("-" * 50)
        for i, node in enumerate(nodes, 1):
            print(f"{i}. {node['name']}")
            print(f"   容忍天数: {node['days']} 天")
            print(f"   前置节点: {node['prev_col']}")
            print()
        print()

def analyze_with_config(file_path, sheet_name, nodes_config):
    """使用指定配置分析数据"""
    try:
        print(f"📁 分析文件: {file_path}")
        print(f"📋 工作表: {sheet_name}")
        print(f"⚙️  节点配置: {len(nodes_config)} 个节点")
        
        # 读取数据
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        print(f"📊 读取数据: {len(df)} 行")
        
        # 筛选正常订单
        if '订单状态' in df.columns:
            df = df[df['订单状态'] == '正常'].copy()
            print(f"✅ 正常订单: {len(df)} 行")
        
        # 获取今天日期
        TODAY = pd.Timestamp(datetime.today().date())
        
        # 转换日期列
        date_cols = ['订单生成时间']
        for node in nodes_config:
            if node["prev_col"] not in date_cols:
                date_cols.append(node["prev_col"])
        
        for col in date_cols:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # 分析超期
        overdue_count = 0
        for _, row in df.iterrows():
            for node in nodes_config:
                current_col = f'{node["name"]}时间'
                if current_col in df.columns and pd.isna(row[current_col]):
                    if node["prev_col"] in df.columns and not pd.isna(row[node["prev_col"]]):
                        due = row[node["prev_col"]] + pd.Timedelta(days=node["days"])
                        if TODAY > due:
                            overdue_count += 1
        
        print(f"⚠️  超期记录: {overdue_count} 条")
        return overdue_count
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return 0

def main():
    """主函数"""
    demo_sheet_configs()
    
    print("=== 使用说明 ===")
    print("1. 运行 tools_ui.py 启动图形界面")
    print("2. 选择Excel文件和工作表")
    print("3. 程序会自动加载对应的预设配置")
    print("4. 可以使用'加载预设'按钮切换配置")
    print("5. 支持自定义节点配置")
    print("6. 配置会自动保存到 config.json")
    print("\n启动命令:")
    print("python3 tools_ui.py")
    print("或者:")
    print("./run.sh")

if __name__ == "__main__":
    main()
