#!/bin/bash

# 订单超期分析工具启动脚本

echo "启动订单超期分析工具..."

# 设置环境变量以消除Tk警告
export TK_SILENCE_DEPRECATION=1

# 检查Python是否安装
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "错误: 未找到Python解释器"
    echo "请确保已安装Python 3.x"
    exit 1
fi

# 检查必要的包是否安装
echo "检查依赖包..."
$PYTHON_CMD -c "import pandas, openpyxl, tkinter" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装必要的依赖包..."
    $PYTHON_CMD -m pip install pandas openpyxl
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        echo "请手动运行: $PYTHON_CMD -m pip install pandas openpyxl"
        exit 1
    fi
fi

# 启动程序
echo "启动UI界面..."
$PYTHON_CMD tools_ui.py

echo "程序已退出"
